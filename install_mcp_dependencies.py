"""
安装 MCP 相关依赖的脚本
"""
import subprocess
import sys

def install_package(package):
    """安装 Python 包"""
    try:
        print(f"正在安装 {package}...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✅ {package} 安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {package} 安装失败: {e}")
        return False

def main():
    """主函数"""
    print("开始安装 MCP 相关依赖...")
    
    # 需要安装的包列表
    packages = [
        "langchain-mcp-adapters",
        "mcp",
        "tavily-python",  # Tavily API 客户端
    ]
    
    success_count = 0
    for package in packages:
        if install_package(package):
            success_count += 1
    
    print(f"\n安装完成: {success_count}/{len(packages)} 个包安装成功")
    
    if success_count == len(packages):
        print("✅ 所有依赖安装成功！现在可以运行测试文件了。")
    else:
        print("⚠️ 部分依赖安装失败，可能需要手动安装。")

if __name__ == "__main__":
    main()
